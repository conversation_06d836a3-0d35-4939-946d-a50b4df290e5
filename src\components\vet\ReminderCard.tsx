
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Pill, Stethoscope } from 'lucide-react';
import { Reminder } from '@/types/vet';
import { getPriorityColor } from '@/utils/vetUtils';

interface ReminderCardProps {
  reminder: Reminder;
}

export const ReminderCard: React.FC<ReminderCardProps> = ({ reminder }) => {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'vaccination':
        return <Pill className="w-4 h-4 text-gray-600" />;
      case 'checkup':
        return <Stethoscope className="w-4 h-4 text-gray-600" />;
      case 'medication':
        return <Pill className="w-4 h-4 text-gray-500" />;
      default:
        return <Calendar className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <Card className="bg-white border border-gray-200 hover:border-gray-300 transition-colors">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg text-gray-900">{reminder.animalName}</CardTitle>
          <Badge className={getPriorityColor(reminder.priority)}>
            {reminder.priority}
          </Badge>
        </div>
        <p className="text-sm text-gray-600">{reminder.animalId}</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            {getTypeIcon(reminder.type)}
            <span className="text-sm font-medium text-gray-900 capitalize">{reminder.type}</span>
          </div>
          
          <p className="text-sm text-gray-700">{reminder.description}</p>
          
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Calendar className="w-4 h-4" />
            Due: {new Date(reminder.dueDate).toLocaleDateString()}
          </div>
          
          <div className="flex gap-2 pt-2">
            <Button size="sm" className="flex-1 bg-gray-800 hover:bg-gray-900 text-white">
              Mark Done
            </Button>
            <Button size="sm" variant="outline" className="flex-1 border-gray-300 hover:bg-gray-50">
              Reschedule
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

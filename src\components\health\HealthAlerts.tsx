
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { AlertTriangle } from 'lucide-react';

interface HealthAlert {
  id: number;
  message: string;
  severity: 'high' | 'medium' | 'low';
  time: string;
}

interface HealthAlertsProps {
  alerts: HealthAlert[];
}

export const HealthAlerts: React.FC<HealthAlertsProps> = ({ alerts }) => {
  return (
    <Card className="bg-white border border-gray-200">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="w-5 h-5 text-gray-600" />
          Health Alerts
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {alerts.map((alert) => (
          <div 
            key={alert.id}
            className={`p-4 rounded-xl border-l-4 ${
              alert.severity === 'high' ? 'border-red-500 bg-red-50' :
              alert.severity === 'medium' ? 'border-gray-500 bg-gray-50' :
              'border-gray-400 bg-gray-50'
            }`}
          >
            <p className="text-sm font-semibold text-gray-900">{alert.message}</p>
            <p className="text-xs text-gray-600 mt-1">{alert.time}</p>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

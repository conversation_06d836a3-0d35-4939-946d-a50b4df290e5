
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { VetVisit, Medication, Reminder, TabType } from '@/types/vet';
import { ReminderCard } from '@/components/vet/ReminderCard';
import { VetDataTable } from '@/components/vet/VetDataTable';
import { SearchAndFilter } from '@/components/vet/SearchAndFilter';
import { TabNavigation } from '@/components/vet/TabNavigation';

const VetTracker = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedTab, setSelectedTab] = useState<TabType>('visits');

  // Mock data
  const vetVisits: VetVisit[] = [
    {
      id: 1,
      animalId: 'C-001',
      animalType: 'Cattle',
      animalName: 'Bessie',
      veterinarian: '<PERSON><PERSON> <PERSON>',
      date: '2024-06-01',
      reason: 'Routine Checkup',
      diagnosis: 'Healthy',
      treatment: 'Vitamin B12 injection',
      status: 'completed',
      cost: 2500,
      nextVisit: '2024-09-01'
    },
    {
      id: 2,
      animalId: 'G-045',
      animalType: 'Goat',
      animalName: 'Billy',
      veterinarian: 'Dr. Jose Cruz',
      date: '2024-05-28',
      reason: 'Limping',
      diagnosis: 'Minor sprain',
      treatment: 'Anti-inflammatory medication',
      status: 'follow-up',
      cost: 1800,
      nextVisit: '2024-06-15'
    },
    {
      id: 3,
      animalId: 'C-012',
      animalType: 'Cattle',
      animalName: 'Ferdinand',
      veterinarian: 'Dr. Maria Santos',
      date: '2024-05-25',
      reason: 'Vaccination',
      diagnosis: 'Preventive care',
      treatment: 'FMD vaccine',
      status: 'completed',
      cost: 1200,
      nextVisit: '2024-11-25'
    },
  ];

  const medications: Medication[] = [
    {
      id: 1,
      animalId: 'G-045',
      animalName: 'Billy',
      medication: 'Meloxicam',
      dosage: '5mg',
      frequency: 'Twice daily',
      startDate: '2024-05-28',
      endDate: '2024-06-05',
      status: 'active',
      veterinarian: 'Dr. Jose Cruz'
    },
    {
      id: 2,
      animalId: 'C-023',
      animalName: 'Moobert',
      medication: 'Antibiotics',
      dosage: '10ml',
      frequency: 'Once daily',
      startDate: '2024-05-20',
      endDate: '2024-05-30',
      status: 'completed',
      veterinarian: 'Dr. Maria Santos'
    },
    {
      id: 3,
      animalId: 'P-008',
      animalName: 'Peppa',
      medication: 'Iron supplement',
      dosage: '2ml',
      frequency: 'Weekly',
      startDate: '2024-06-01',
      endDate: '2024-07-01',
      status: 'active',
      veterinarian: 'Dr. Ana Lopez'
    },
  ];

  const upcomingReminders: Reminder[] = [
    {
      id: 1,
      type: 'vaccination',
      animalId: 'C-025',
      animalName: 'Daisy',
      description: 'Annual FMD vaccination due',
      dueDate: '2024-06-10',
      priority: 'high'
    },
    {
      id: 2,
      type: 'checkup',
      animalId: 'G-045',
      animalName: 'Billy',
      description: 'Follow-up visit for sprain',
      dueDate: '2024-06-15',
      priority: 'medium'
    },
    {
      id: 3,
      type: 'medication',
      animalId: 'P-008',
      animalName: 'Peppa',
      description: 'Iron supplement administration',
      dueDate: '2024-06-08',
      priority: 'medium'
    },
  ];

  const filteredData = selectedTab === 'visits' ? vetVisits : medications;
  const searchFiltered = filteredData.filter(item => 
    item.animalName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.animalId?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Veterinary Tracker</h1>
          <p className="text-gray-700 mt-1">Medical records, treatments, and health schedules</p>
        </div>
        <Button className="bg-green-600 hover:bg-green-700 text-white">
          <Plus className="w-4 h-4 mr-2" />
          Schedule Visit
        </Button>
      </div>

      {/* Tab Navigation */}
      <TabNavigation selectedTab={selectedTab} onTabChange={setSelectedTab} />

      {/* Search and Filter */}
      <SearchAndFilter 
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        filterStatus={filterStatus}
        onFilterChange={setFilterStatus}
      />

      {/* Content */}
      {selectedTab === 'reminders' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {upcomingReminders.map((reminder) => (
            <ReminderCard key={reminder.id} reminder={reminder} />
          ))}
        </div>
      ) : (
        <VetDataTable data={searchFiltered} selectedTab={selectedTab} />
      )}
    </div>
  );
};

export default VetTracker;

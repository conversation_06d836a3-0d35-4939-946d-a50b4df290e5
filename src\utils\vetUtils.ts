
export const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-gray-100 text-gray-800';
    case 'active':
      return 'bg-gray-200 text-gray-800';
    case 'follow-up':
      return 'bg-gray-300 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'high':
      return 'bg-red-100 text-red-800';
    case 'medium':
      return 'bg-gray-200 text-gray-800';
    case 'low':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-PH', {
    style: 'currency',
    currency: 'PHP'
  }).format(amount);
};

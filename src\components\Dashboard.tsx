
import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LivestockCard } from './LivestockCard';
import { HealthStatusOverview } from './HealthStatusOverview';
import { TrendsChart } from './TrendsChart';
import { AlertBadge } from './AlertBadge';
import { Button } from '@/components/ui/button';
import { ArrowRight, Plus, BarChart3, Activity } from 'lucide-react';
import { Link } from 'react-router-dom';

const Dashboard = () => {
  const livestockData = [
    { type: 'Cattle', count: 156, breed: 'Holstein, Brahman', icon: '🐄' },
    { type: 'Carabao', count: 89, breed: 'Philippine Carabao', icon: '🐃' },
    { type: 'Goats', count: 234, breed: 'Boer, Anglo-Nubian', icon: '🐐' },
    { type: 'Chickens', count: 1247, breed: 'Rhode Island Red', icon: '🐓' },
  ];

  const healthAlerts = [
    { type: 'vaccination', count: 12, severity: 'medium' as const },
    { type: 'illness', count: 3, severity: 'high' as const },
    { type: 'checkup', count: 28, severity: 'low' as const },
  ];

  return (
    <div className="space-y-8 animate-fade-in">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-green-500 to-green-700 rounded-3xl p-8 text-white">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold mb-2">
              Welcome back, Juan! 👋
            </h1>
            <p className="text-green-100 text-lg">
              Here's what's happening with your livestock today
            </p>
          </div>
          <div className="flex flex-wrap gap-2">
            {healthAlerts.map((alert, index) => (
              <AlertBadge
                key={alert.type}
                type={alert.type}
                count={alert.count}
                severity={alert.severity}
                className="animate-bounce-gentle"
                style={{ animationDelay: `${index * 0.2}s` }}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Livestock Overview Cards */}
      <div>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Livestock Overview</h2>
          <Link to="/inventory">
            <Button variant="outline" className="gap-2 border-green-300 text-green-700 hover:bg-green-50">
              View All <ArrowRight className="w-4 h-4" />
            </Button>
          </Link>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {livestockData.map((livestock, index) => (
            <LivestockCard
              key={livestock.type}
              livestock={livestock}
              className="animate-slide-in"
              style={{ animationDelay: `${index * 0.1}s` }}
            />
          ))}
        </div>
      </div>

      {/* Health Status and Trends */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <HealthStatusOverview />
        </div>
        <div className="lg:col-span-2">
          <TrendsChart />
        </div>
      </div>

      {/* Quick Actions */}
      <Card className="bg-white rounded-3xl border-gray-200 hover:border-green-300 transition-all duration-300 shadow-sm">
        <CardHeader>
          <CardTitle className="text-2xl font-semibold text-gray-700 flex items-center gap-3">
            <span className="animate-bounce-gentle">🚀</span>
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link to="/inventory">
              <button className="w-full bg-white rounded-2xl p-6 hover:bg-green-50 transition-all duration-300 group border border-gray-100 hover:border-green-200">
                <div className="text-3xl mb-3 group-hover:animate-bounce-gentle">
                  <Plus className="w-8 h-8 mx-auto text-green-600" />
                </div>
                <h3 className="font-semibold text-gray-700 mb-2">Add Livestock</h3>
                <p className="text-sm text-gray-600">Register new animals to your inventory</p>
              </button>
            </Link>
            
            <Link to="/health">
              <button className="w-full bg-white rounded-2xl p-6 hover:bg-green-50 transition-all duration-300 group border border-gray-100 hover:border-green-200">
                <div className="text-3xl mb-3 group-hover:animate-bounce-gentle">
                  <Activity className="w-8 h-8 mx-auto text-green-600" />
                </div>
                <h3 className="font-semibold text-gray-700 mb-2">Health Check</h3>
                <p className="text-sm text-gray-600">AI disease prediction & health monitoring</p>
              </button>
            </Link>

            <button className="bg-white rounded-2xl p-6 hover:bg-green-50 transition-all duration-300 group border border-gray-100 hover:border-green-200">
              <div className="text-3xl mb-3 group-hover:animate-bounce-gentle">
                <BarChart3 className="w-8 h-8 mx-auto text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-700 mb-2">Generate Report</h3>
              <p className="text-sm text-gray-600">Create detailed analytics reports</p>
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;

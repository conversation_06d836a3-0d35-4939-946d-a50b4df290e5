
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface LivestockItem {
  id: string;
  type: string;
  breed: string;
  age: string;
  status: string;
  owner: string;
  lastCheckup: string;
}

interface InventoryTableProps {
  data: LivestockItem[];
}

export const InventoryTable: React.FC<InventoryTableProps> = ({ data }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Healthy': return 'text-green-600 bg-green-100';
      case 'Treatment': return 'text-gray-600 bg-gray-100';
      case 'Critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <Card className="bg-white border border-gray-200">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Livestock Records ({data.length})</span>
          <div className="text-sm font-normal text-gray-600">
            Last updated: {new Date().toLocaleDateString()}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-semibold text-gray-700">ID</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">Type & Breed</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">Age</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">Status</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">Owner</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">Last Checkup</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody>
              {data.map((item) => (
                <tr key={item.id} className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                  <td className="py-4 px-4 font-mono text-sm font-semibold text-gray-700">{item.id}</td>
                  <td className="py-4 px-4">
                    <div>
                      <div className="font-semibold">{item.type}</div>
                      <div className="text-sm text-gray-600">{item.breed}</div>
                    </div>
                  </td>
                  <td className="py-4 px-4 text-gray-700">{item.age}</td>
                  <td className="py-4 px-4">
                    <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(item.status)}`}>
                      {item.status}
                    </span>
                  </td>
                  <td className="py-4 px-4 text-gray-700">{item.owner}</td>
                  <td className="py-4 px-4 text-gray-700">{item.lastCheckup}</td>
                  <td className="py-4 px-4">
                    <div className="flex gap-2">
                      <Button variant="ghost" size="sm" className="hover:bg-gray-100">Edit</Button>
                      <Button variant="ghost" size="sm" className="hover:bg-gray-100">View</Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};


import React from 'react';
import { Button } from '@/components/ui/button';
import { Stethoscope, Pill, Calendar } from 'lucide-react';
import { TabType } from '@/types/vet';

interface TabNavigationProps {
  selectedTab: TabType;
  onTabChange: (tab: TabType) => void;
}

export const TabNavigation: React.FC<TabNavigationProps> = ({ selectedTab, onTabChange }) => {
  const tabs = [
    { id: 'visits' as TabType, label: 'Vet Visits', icon: Stethoscope },
    { id: 'medications' as TabType, label: 'Medications', icon: Pill },
    { id: 'reminders' as TabType, label: 'Reminders', icon: Calendar },
  ];

  return (
    <div className="flex flex-wrap gap-3">
      {tabs.map((tab) => {
        const Icon = tab.icon;
        return (
          <Button
            key={tab.id}
            variant={selectedTab === tab.id ? "default" : "outline"}
            onClick={() => onTabChange(tab.id)}
            className={
              selectedTab === tab.id 
                ? "bg-gray-800 hover:bg-gray-900 text-white" 
                : "bg-gray-100 hover:bg-gray-200 border-gray-300"
            }
          >
            <Icon className="w-4 h-4" />
            {tab.label}
          </Button>
        );
      })}
    </div>
  );
};

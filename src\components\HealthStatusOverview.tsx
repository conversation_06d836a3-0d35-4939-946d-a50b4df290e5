
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';

export const HealthStatusOverview = () => {
  const healthData = [
    { status: 'Healthy', count: 1456, percentage: 87, color: 'text-forest-600', bgColor: 'bg-forest-500' },
    { status: 'Treatment', count: 178, percentage: 11, color: 'text-amber-600', bgColor: 'bg-amber-500' },
    { status: 'Critical', count: 32, percentage: 2, color: 'text-red-600', bgColor: 'bg-red-500' },
  ];

  return (
    <Card className="bg-white/90 backdrop-blur-sm rounded-3xl border-gray-200 hover:border-forest-300 transition-all duration-300 h-full shadow-sm">
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-forest-700 flex items-center gap-2">
          <span className="animate-pulse-glow">🏥</span>
          Health Status Overview
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {healthData.map((item, index) => (
          <div key={item.status} className="space-y-3 animate-slide-in" style={{ animationDelay: `${index * 0.1}s` }}>
            <div className="flex justify-between items-center">
              <span className={`font-semibold ${item.color}`}>{item.status}</span>
              <span className="text-gray-800 font-bold">{item.count}</span>
            </div>
            
            <div className="relative">
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className={`h-3 rounded-full ${item.bgColor} transition-all duration-1000 animate-fade-in`}
                  style={{ 
                    width: `${item.percentage}%`,
                    animationDelay: `${index * 0.2 + 0.5}s`
                  }}
                />
              </div>
              <span className="absolute right-0 top-4 text-xs text-gray-500">
                {item.percentage}%
              </span>
            </div>
          </div>
        ))}
        
        <div className="mt-6 p-4 bg-white/60 backdrop-blur-sm rounded-2xl border border-gray-100">
          <h4 className="font-semibold text-forest-700 mb-2 flex items-center gap-2">
            <span className="animate-bounce-gentle">📋</span>
            Recent Health Actions
          </h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between text-gray-600">
              <span>Vaccinations completed</span>
              <span className="text-forest-600 font-semibold">45 today</span>
            </div>
            <div className="flex justify-between text-gray-600">
              <span>Vet visits scheduled</span>
              <span className="text-amber-600 font-semibold">12 this week</span>
            </div>
            <div className="flex justify-between text-gray-600">
              <span>Health reports generated</span>
              <span className="text-blue-600 font-semibold">8 this month</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

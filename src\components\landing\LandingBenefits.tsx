
import React from 'react';
import { CheckCircle, TrendingUp, Users, Award } from 'lucide-react';

const benefits = [
  "Increase livestock productivity by up to 30%",
  "Reduce disease outbreaks with early detection",
  "Optimize feed costs and resource allocation",
  "Generate detailed compliance reports",
  "24/7 monitoring and alert systems",
  "Reduce operational costs by 25%"
];

const testimonials = [
  {
    name: "<PERSON>",
    role: "Ranch Owner",
    company: "Green Valley Ranch",
    quote: "AgriPulse transformed our operation. We've seen a 25% increase in productivity.",
    avatar: "SJ"
  },
  {
    name: "<PERSON>",
    role: "Farm Manager",
    company: "Sunrise Farms",
    quote: "The health monitoring system caught diseases we would have missed entirely.",
    avatar: "MC"
  }
];

export const LandingBenefits: React.FC = () => {
  return (
    <section className="py-24 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-green-50 via-white to-blue-50">
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <div className="space-y-8">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Proven results for livestock operations
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                Join thousands of farmers who have transformed their operations with AgriPulse and achieved measurable results.
              </p>
            </div>
            
            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center gap-4 p-4 rounded-xl bg-white/80 backdrop-blur-sm hover:bg-white transition-colors duration-300">
                  <div className="flex-shrink-0">
                    <CheckCircle className="w-6 h-6 text-green-600" />
                  </div>
                  <span className="text-gray-700 font-medium">{benefit}</span>
                </div>
              ))}
            </div>

            {/* Testimonials */}
            <div className="space-y-6 pt-8">
              <h3 className="text-2xl font-semibold text-gray-900">What our customers say</h3>
              {testimonials.map((testimonial, index) => (
                <div key={index} className="bg-white p-6 rounded-2xl shadow-lg border border-gray-100">
                  <p className="text-gray-700 mb-4 italic">"{testimonial.quote}"</p>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                      {testimonial.avatar}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{testimonial.name}</div>
                      <div className="text-sm text-gray-600">{testimonial.role}, {testimonial.company}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="relative">
            {/* Stats card */}
            <div className="bg-white rounded-3xl p-8 shadow-2xl border border-gray-100 relative z-10">
              <div className="text-center mb-8">
                <div className="text-5xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-2">
                  10,000+
                </div>
                <div className="text-gray-600 text-lg">Livestock tracked daily</div>
              </div>
              
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center p-4 rounded-2xl bg-gradient-to-r from-green-50 to-green-100">
                  <TrendingUp className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">98%</div>
                  <div className="text-sm text-gray-600">Uptime</div>
                </div>
                <div className="text-center p-4 rounded-2xl bg-gradient-to-r from-blue-50 to-blue-100">
                  <Users className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">50+</div>
                  <div className="text-sm text-gray-600">Countries</div>
                </div>
                <div className="text-center p-4 rounded-2xl bg-gradient-to-r from-purple-50 to-purple-100">
                  <Award className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">24/7</div>
                  <div className="text-sm text-gray-600">Support</div>
                </div>
                <div className="text-center p-4 rounded-2xl bg-gradient-to-r from-yellow-50 to-orange-100">
                  <CheckCircle className="w-8 h-8 text-orange-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">5★</div>
                  <div className="text-sm text-gray-600">Rating</div>
                </div>
              </div>
            </div>
            
            {/* Background decoration */}
            <div className="absolute -top-8 -right-8 w-24 h-24 bg-gradient-to-r from-green-400 to-blue-400 rounded-full opacity-20 animate-pulse"></div>
            <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-20 animate-pulse" style={{ animationDelay: '1s' }}></div>
          </div>
        </div>
      </div>
    </section>
  );
};

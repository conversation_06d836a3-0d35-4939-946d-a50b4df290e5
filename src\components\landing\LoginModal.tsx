
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Eye, EyeOff, Loader2, Zap, ExternalLink } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const LoginModal: React.FC<LoginModalProps> = ({ isOpen, onClose }) => {
  const { devBypass } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleDevBypass = () => {
    devBypass();
    toast({
      title: "Developer Access",
      description: "Logged in with developer credentials.",
    });
    onClose();
  };

  const handleGoToLogin = () => {
    navigate('/login');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="w-full max-w-md">
        <Card className="border-0 shadow-xl bg-white">
          <CardHeader className="space-y-1">
            <div className="flex justify-between items-center">
              <CardTitle className="text-2xl">Welcome to AgriPulse</CardTitle>
              <Button 
                variant="ghost" 
                size="icon"
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </Button>
            </div>
            <CardDescription>
              Choose how you'd like to continue
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={handleGoToLogin}
              className="w-full h-12 bg-black hover:bg-gray-800 text-white"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Go to Full Sign In Page
            </Button>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-gray-200" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-white px-2 text-gray-500">Or</span>
              </div>
            </div>

            <Button
              variant="outline"
              onClick={handleDevBypass}
              className="w-full h-12 text-gray-600 border-gray-300 hover:bg-gray-50"
            >
              <Zap className="w-4 h-4 mr-2" />
              Developer Access (Bypass)
            </Button>
            <p className="text-xs text-gray-500 text-center">
              For development purposes only
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

import React from 'react';
import { cn } from '@/lib/utils';

interface AlertBadgeProps {
  type: string;
  count: number;
  severity: 'low' | 'medium' | 'high';
  className?: string;
  style?: React.CSSProperties;
}

export const AlertBadge: React.FC<AlertBadgeProps> = ({ 
  type, 
  count, 
  severity, 
  className,
  style 
}) => {
  const getAlertIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'vaccination':
        return '💉';
      case 'illness':
        return '🚨';
      case 'checkup':
        return '🔍';
      default:
        return '📋';
    }
  };

  const getSeverityStyles = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-gradient-to-r from-red-500 to-red-700 text-white animate-pulse-glow';
      case 'medium':
        return 'bg-gradient-to-r from-neutral-500 to-neutral-600 text-white';
      case 'low':
        return 'bg-gradient-to-r from-neutral-400 to-neutral-500 text-white';
      default:
        return 'bg-gradient-to-r from-gray-500 to-gray-700 text-white';
    }
  };

  return (
    <div 
      className={cn(
        "glass-dark rounded-full px-4 py-2 flex items-center gap-2 hover:scale-105 transition-all duration-300 cursor-pointer",
        getSeverityStyles(severity),
        className
      )}
      style={style}
    >
      <span className="text-lg animate-bounce-gentle">{getAlertIcon(type)}</span>
      <div className="flex flex-col">
        <span className="text-xs font-semibold uppercase tracking-wide">
          {type}
        </span>
        <span className="text-lg font-bold leading-none">
          {count}
        </span>
      </div>
    </div>
  );
};

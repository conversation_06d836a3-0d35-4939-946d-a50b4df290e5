
import React from 'react';
import { Button } from '@/components/ui/button';
import { User, MapPin, Calendar, Users, Heart } from 'lucide-react';

interface Profile {
  id: string;
  name: string;
  farmName: string;
  location: string;
  joinedDate: string;
  friendsCount: number;
  isFollowing: boolean;
  avatar?: string;
}

interface ProfileCardProps {
  profile: Profile;
  onToggleFollow: (profileId: string) => void;
}

export const ProfileCard: React.FC<ProfileCardProps> = ({ profile, onToggleFollow }) => {
  return (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300">
      <div className="flex items-center gap-4 mb-4">
        <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center">
          {profile.avatar ? (
            <img src={profile.avatar} alt={profile.name} className="w-full h-full rounded-full object-cover" />
          ) : (
            <User className="w-8 h-8 text-white" />
          )}
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900">{profile.name}</h3>
          <p className="text-green-600 font-medium">{profile.farmName}</p>
        </div>
        <Button
          variant={profile.isFollowing ? "outline" : "default"}
          size="sm"
          onClick={() => onToggleFollow(profile.id)}
          className={profile.isFollowing ? "text-green-600 border-green-600 hover:bg-green-50" : ""}
        >
          {profile.isFollowing ? (
            <>
              <Heart className="w-4 h-4 mr-1 fill-current" />
              Following
            </>
          ) : (
            <>
              <Users className="w-4 h-4 mr-1" />
              Follow
            </>
          )}
        </Button>
      </div>
      
      <div className="space-y-2 text-sm text-gray-600">
        <div className="flex items-center gap-2">
          <MapPin className="w-4 h-4" />
          <span>{profile.location}</span>
        </div>
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4" />
          <span>Joined {profile.joinedDate}</span>
        </div>
        <div className="flex items-center gap-2">
          <Users className="w-4 h-4" />
          <span>{profile.friendsCount} connections</span>
        </div>
      </div>
    </div>
  );
};


@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 250 250 250;
    --foreground: 23 23 23;

    --card: 255 255 255;
    --card-foreground: 23 23 23;

    --popover: 255 255 255;
    --popover-foreground: 23 23 23;

    --primary: 64 64 64;
    --primary-foreground: 250 250 250;

    --secondary: 75 85 99;
    --secondary-foreground: 250 250 250;

    --muted: 229 231 235;
    --muted-foreground: 75 85 99;

    --accent: 107 114 128;
    --accent-foreground: 250 250 250;

    --destructive: 239 68 68;
    --destructive-foreground: 250 250 250;

    --border: 209 213 219;
    --input: 209 213 219;
    --ring: 107 114 128;

    --radius: 0.5rem;

    --sidebar-background: 255 255 255;
    --sidebar-foreground: 23 23 23;
    --sidebar-primary: 64 64 64;
    --sidebar-primary-foreground: 250 250 250;
    --sidebar-accent: 229 231 235;
    --sidebar-accent-foreground: 23 23 23;
    --sidebar-border: 209 213 219;
    --sidebar-ring: 107 114 128;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gray-50 text-foreground font-sans;
    background: #fafafa;
    min-height: 100vh;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-400 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500;
  }
}

@layer components {
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  .gradient-text {
    background: linear-gradient(135deg, #404040, #1f2937);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Flat design utilities */
  .flat-card {
    @apply bg-white border border-gray-200 rounded-lg shadow-none;
  }

  .flat-button {
    @apply bg-gray-800 text-white hover:bg-gray-900 rounded-md shadow-none;
  }

  .flat-input {
    @apply border-gray-300 rounded-md shadow-none focus:border-gray-500 focus:ring-0;
  }
}

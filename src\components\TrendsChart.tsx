
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from 'recharts';

export const TrendsChart = () => {
  const trendsData = [
    { month: 'Jan', births: 45, sales: 23, mortality: 5, health: 92 },
    { month: 'Feb', births: 52, sales: 31, mortality: 3, health: 94 },
    { month: 'Mar', births: 48, sales: 28, mortality: 7, health: 91 },
    { month: 'Apr', births: 61, sales: 35, mortality: 4, health: 93 },
    { month: 'May', births: 55, sales: 42, mortality: 6, health: 89 },
    { month: 'Jun', births: 67, sales: 38, mortality: 2, health: 96 },
  ];

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white/95 backdrop-blur-sm rounded-lg p-4 border border-gray-200 shadow-lg">
          <p className="text-forest-700 font-semibold mb-2">{`${label} 2024`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {`${entry.dataKey}: ${entry.value}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="bg-white/90 backdrop-blur-sm rounded-3xl border-gray-200 hover:border-forest-300 transition-all duration-300 h-full shadow-sm">
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-forest-700 flex items-center gap-2">
          <span className="animate-bounce-gentle">📈</span>
          Monthly Trends & Predictions
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Main Trends Chart */}
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={trendsData}>
                <defs>
                  <linearGradient id="birthsGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#22c55e" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#22c55e" stopOpacity={0}/>
                  </linearGradient>
                  <linearGradient id="salesGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#fbbf24" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#fbbf24" stopOpacity={0}/>
                  </linearGradient>
                  <linearGradient id="mortalityGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#ef4444" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#ef4444" stopOpacity={0}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                <XAxis 
                  dataKey="month" 
                  stroke="#6b7280"
                  fontSize={12}
                />
                <YAxis 
                  stroke="#6b7280"
                  fontSize={12}
                />
                <Tooltip content={<CustomTooltip />} />
                <Area
                  type="monotone"
                  dataKey="births"
                  stroke="#22c55e"
                  fillOpacity={1}
                  fill="url(#birthsGradient)"
                  strokeWidth={2}
                />
                <Area
                  type="monotone"
                  dataKey="sales"
                  stroke="#fbbf24"
                  fillOpacity={1}
                  fill="url(#salesGradient)"
                  strokeWidth={2}
                />
                <Area
                  type="monotone"
                  dataKey="mortality"
                  stroke="#ef4444"
                  fillOpacity={1}
                  fill="url(#mortalityGradient)"
                  strokeWidth={2}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>

          {/* Trend Indicators */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-4 text-center border border-gray-100">
              <div className="text-2xl mb-2 animate-bounce-gentle">📈</div>
              <h4 className="font-semibold text-forest-600 mb-1">Births Trend</h4>
              <p className="text-sm text-gray-600">+12% from last month</p>
              <div className="mt-2 text-xs text-forest-600">Predicted: 72 next month</div>
            </div>
            
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-4 text-center border border-gray-100">
              <div className="text-2xl mb-2 animate-bounce-gentle">💰</div>
              <h4 className="font-semibold text-amber-600 mb-1">Sales Trend</h4>
              <p className="text-sm text-gray-600">+8% from last month</p>
              <div className="mt-2 text-xs text-amber-600">Predicted: 41 next month</div>
            </div>
            
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-4 text-center border border-gray-100">
              <div className="text-2xl mb-2 animate-bounce-gentle">⚕️</div>
              <h4 className="font-semibold text-red-600 mb-1">Health Score</h4>
              <p className="text-sm text-gray-600">96% average health</p>
              <div className="mt-2 text-xs text-green-600">Excellent condition</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

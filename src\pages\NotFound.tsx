
import { useLocation } from "react-router-dom";
import { useEffect } from "react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="text-center p-8">
        <div className="mb-6">
          <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-green-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <span className="text-white text-3xl">🌾</span>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-2">404</h1>
          <h2 className="text-2xl font-semibold text-green-600 mb-4">Page Not Found</h2>
        </div>
        
        <div className="max-w-md mx-auto mb-8">
          <p className="text-lg text-gray-600 mb-4">
            Oops! The page you're looking for doesn't exist in AgriPulse.
          </p>
          <p className="text-gray-500">
            It might have been moved, deleted, or you entered the wrong URL.
          </p>
        </div>

        <div className="space-y-4">
          <a 
            href="/" 
            className="inline-block bg-green-600 hover:bg-green-700 text-white font-semibold px-6 py-3 rounded-lg transition-colors"
          >
            Return to Dashboard
          </a>
          
          <div className="text-gray-500">
            <p>Need help? Visit our support center or contact us.</p>
          </div>
        </div>

        <div className="mt-12 pt-8 border-t border-gray-200">
          <p className="text-sm text-gray-400">
            © 2024 AgriPulse. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
};

export default NotFound;

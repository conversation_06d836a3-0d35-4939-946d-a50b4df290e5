
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>R<PERSON>, Star, Shield, Zap } from 'lucide-react';

interface LandingCTAProps {
  onGetStarted: () => void;
}

export const LandingCTA: React.FC<LandingCTAProps> = ({ onGetStarted }) => {
  return (
    <section className="py-24 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 opacity-50">
        <div className="absolute inset-0 bg-gray-900"></div>
      </div>
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-green-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-float"></div>
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-float" style={{ animationDelay: '2s' }}></div>

      <div className="relative max-w-6xl mx-auto text-center">
        {/* Trust indicators */}
        <div className="flex justify-center items-center gap-8 mb-12 flex-wrap">
          <div className="flex items-center gap-2 text-gray-300">
            <Shield className="w-5 h-5 text-green-400" />
            <span className="text-sm">SOC 2 Certified</span>
          </div>
          <div className="flex items-center gap-2 text-gray-300">
            <Star className="w-5 h-5 text-yellow-400" />
            <span className="text-sm">4.9/5 Rating</span>
          </div>
          <div className="flex items-center gap-2 text-gray-300">
            <Zap className="w-5 h-5 text-blue-400" />
            <span className="text-sm">99.9% Uptime</span>
          </div>
        </div>

        <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
          Ready to transform your 
          <span className="block bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
            livestock management?
          </span>
        </h2>
        
        <p className="text-xl text-gray-300 mb-10 max-w-3xl mx-auto leading-relaxed">
          Join thousands of farmers who have revolutionized their operations. 
          Start your free trial today and experience the future of agriculture.
        </p>
        
        <div className="space-y-6">
          <Button 
            size="lg" 
            onClick={onGetStarted} 
            className="text-lg px-12 py-6 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 shadow-2xl hover:shadow-green-500/25 transition-all duration-300 transform hover:scale-105"
          >
            Start Free Trial Today
            <ArrowRight className="ml-2 w-6 h-6" />
          </Button>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center text-gray-400 text-sm">
            <span>✓ No credit card required</span>
            <span>✓ 14-day free trial</span>
            <span>✓ Cancel anytime</span>
          </div>
        </div>

        {/* Additional trust section */}
        <div className="mt-16 pt-16 border-t border-gray-700">
          <p className="text-gray-400 mb-6">Trusted by leading agricultural companies worldwide</p>
          <div className="flex justify-center items-center gap-12 opacity-60 flex-wrap">
            <div className="text-2xl font-bold text-gray-500">ACME FARMS</div>
            <div className="text-2xl font-bold text-gray-500">GREENFIELD</div>
            <div className="text-2xl font-bold text-gray-500">HARVEST CO</div>
            <div className="text-2xl font-bold text-gray-500">AGRITECH</div>
          </div>
        </div>
      </div>
    </section>
  );
};

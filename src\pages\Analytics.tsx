
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Pie<PERSON>hart, Pie, Cell, LineChart, Line } from 'recharts';
import { Download, Calendar, TrendingUp, Users, DollarSign, BarChart3 } from 'lucide-react';

const Analytics = () => {
  const [selectedTimeRange, setSelectedTimeRange] = useState('3months');
  const [selectedMetric, setSelectedMetric] = useState('population');

  // Mock data for charts
  const populationData = [
    { month: 'Jan', cattle: 150, goats: 220, chickens: 890, pigs: 45 },
    { month: 'Feb', cattle: 155, goats: 230, chickens: 920, pigs: 48 },
    { month: 'Mar', cattle: 160, goats: 240, chickens: 950, pigs: 52 },
    { month: 'Apr', cattle: 158, goats: 235, chickens: 980, pigs: 55 },
    { month: 'May', cattle: 162, goats: 245, chickens: 1020, pigs: 58 },
    { month: 'Jun', cattle: 165, goats: 250, chickens: 1050, pigs: 60 },
  ];

  const revenueData = [
    { month: 'Jan', revenue: 180000, expenses: 120000, profit: 60000 },
    { month: 'Feb', revenue: 195000, expenses: 125000, profit: 70000 },
    { month: 'Mar', revenue: 210000, expenses: 130000, profit: 80000 },
    { month: 'Apr', revenue: 205000, expenses: 128000, profit: 77000 },
    { month: 'May', revenue: 220000, expenses: 135000, profit: 85000 },
    { month: 'Jun', revenue: 225000, expenses: 140000, profit: 85000 },
  ];

  const mortalityData = [
    { month: 'Jan', mortality: 2.1, target: 2.0 },
    { month: 'Feb', mortality: 1.8, target: 2.0 },
    { month: 'Mar', mortality: 1.9, target: 2.0 },
    { month: 'Apr', mortality: 1.6, target: 2.0 },
    { month: 'May', mortality: 1.5, target: 2.0 },
    { month: 'Jun', mortality: 1.4, target: 2.0 },
  ];

  const distributionData = [
    { name: 'Chickens', value: 65, count: 1050 },
    { name: 'Cattle', value: 17, count: 165 },
    { name: 'Goats', value: 12, count: 250 },
    { name: 'Pigs', value: 6, count: 60 },
  ];

  const COLORS = ['#3b82f6', '#22c55e', '#f59e0b', '#ef4444'];

  const productivityData = [
    { name: 'Milk Production', current: 2450, target: 2600, percentage: 94 },
    { name: 'Egg Production', current: 1890, target: 2000, percentage: 94 },
    { name: 'Feed Efficiency', current: 85, target: 90, percentage: 94 },
    { name: 'Birth Rate', current: 94, target: 95, percentage: 99 },
  ];

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900">{`${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${String(entry.dataKey).charAt(0).toUpperCase() + String(entry.dataKey).slice(1)}: ${entry.value}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const getStatsForMetric = () => {
    switch (selectedMetric) {
      case 'revenue':
        return [
          {
            title: 'Total Revenue',
            value: '₱2.25M',
            change: '+12.5% from last month',
            icon: DollarSign,
            color: 'text-green-600'
          },
          {
            title: 'Total Expenses',
            value: '₱1.40M',
            change: '+8.2% from last month',
            icon: TrendingUp,
            color: 'text-red-600'
          },
          {
            title: 'Net Profit',
            value: '₱850K',
            change: '+22.3% from last month',
            icon: BarChart3,
            color: 'text-blue-600'
          },
          {
            title: 'Profit Margin',
            value: '37.8%',
            change: '+3.1% from last month',
            icon: Users,
            color: 'text-purple-600'
          },
        ];
      case 'mortality':
        return [
          {
            title: 'Mortality Rate',
            value: '1.4%',
            change: '-0.1% from last month',
            icon: TrendingUp,
            color: 'text-green-600'
          },
          {
            title: 'Deaths This Month',
            value: '21',
            change: '-3 from last month',
            icon: Users,
            color: 'text-red-600'
          },
          {
            title: 'Health Score',
            value: '94.2%',
            change: '+1.2% from last month',
            icon: BarChart3,
            color: 'text-green-600'
          },
          {
            title: 'Vet Visits',
            value: '45',
            change: '+5 from last month',
            icon: DollarSign,
            color: 'text-blue-600'
          },
        ];
      default:
        return [
          {
            title: 'Total Animals',
            value: '1,520',
            change: '+5.2% from last month',
            icon: Users,
            color: 'text-green-600'
          },
          {
            title: 'New Births',
            value: '78',
            change: '+12.5% from last month',
            icon: TrendingUp,
            color: 'text-blue-600'
          },
          {
            title: 'Growth Rate',
            value: '5.1%',
            change: '+0.4% from last month',
            icon: BarChart3,
            color: 'text-green-600'
          },
          {
            title: 'Active Farms',
            value: '24',
            change: '+2 new this month',
            icon: DollarSign,
            color: 'text-purple-600'
          },
        ];
    }
  };

  const getChartData = () => {
    switch (selectedMetric) {
      case 'revenue':
        return revenueData;
      case 'mortality':
        return mortalityData;
      default:
        return populationData;
    }
  };

  const renderChart = () => {
    const data = getChartData();
    
    if (selectedMetric === 'revenue') {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar dataKey="revenue" fill="#22c55e" name="Revenue" />
            <Bar dataKey="expenses" fill="#ef4444" name="Expenses" />
            <Bar dataKey="profit" fill="#3b82f6" name="Profit" />
          </BarChart>
        </ResponsiveContainer>
      );
    }
    
    if (selectedMetric === 'mortality') {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Line type="monotone" dataKey="mortality" stroke="#ef4444" name="Mortality Rate %" />
            <Line type="monotone" dataKey="target" stroke="#6b7280" strokeDasharray="5 5" name="Target" />
          </LineChart>
        </ResponsiveContainer>
      );
    }
    
    return (
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" />
          <YAxis />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          <Bar dataKey="cattle" fill="#22c55e" name="Cattle" />
          <Bar dataKey="goats" fill="#3b82f6" name="Goats" />
          <Bar dataKey="chickens" fill="#6366f1" name="Chickens" />
          <Bar dataKey="pigs" fill="#ef4444" name="Pigs" />
        </BarChart>
      </ResponsiveContainer>
    );
  };

  const getChartTitle = () => {
    switch (selectedMetric) {
      case 'revenue':
        return 'Revenue & Expense Trends';
      case 'mortality':
        return 'Mortality Rate Trends';
      default:
        return 'Livestock Population Trends';
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-700 mt-1">Comprehensive insights into your livestock operations</p>
        </div>
        <Button className="bg-green-600 hover:bg-green-700 text-white">
          <Download className="w-4 h-4 mr-2" />
          Export Report
        </Button>
      </div>

      {/* Metric and Time Range Filters */}
      <div className="space-y-4">
        <div className="flex flex-wrap gap-4">
          <div className="flex gap-2">
            {[
              { value: 'population', label: 'Population' },
              { value: 'revenue', label: 'Revenue' },
              { value: 'mortality', label: 'Mortality' },
            ].map((filter) => (
              <Button
                key={filter.value}
                variant={selectedMetric === filter.value ? 'default' : 'outline'}
                onClick={() => setSelectedMetric(filter.value)}
                className="text-gray-900"
              >
                {filter.label}
              </Button>
            ))}
          </div>
        </div>
        
        <div className="flex gap-2">
          {[
            { value: '3months', label: '3 Months' },
            { value: '6months', label: '6 Months' },
            { value: '1year', label: '1 Year' }
          ].map((filter) => (
            <Button
              key={filter.value}
              variant={selectedTimeRange === filter.value ? 'default' : 'outline'}
              onClick={() => setSelectedTimeRange(filter.value)}
              className="text-gray-900"
            >
              {filter.label}
            </Button>
          ))}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {getStatsForMetric().map((stat, index) => (
          <Card key={index} className="bg-white border border-gray-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  <p className={`text-sm ${stat.color}`}>{stat.change}</p>
                </div>
                <stat.icon className={`w-8 h-8 ${stat.color}`} />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Dynamic Chart based on selected metric */}
        <Card className="bg-white border border-gray-200">
          <CardHeader>
            <CardTitle className="text-gray-900">{getChartTitle()}</CardTitle>
          </CardHeader>
          <CardContent>
            {renderChart()}
          </CardContent>
        </Card>

        {/* Animal Distribution */}
        <Card className="bg-white border border-gray-200">
          <CardHeader>
            <CardTitle className="text-gray-900">Animal Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={distributionData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name} ${value}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {distributionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Productivity Metrics */}
      <Card className="bg-white border border-gray-200">
        <CardHeader>
          <CardTitle className="text-gray-900">Productivity Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {productivityData.map((metric, index) => (
              <div key={index}>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-900">{metric.name}</span>
                  <span className="text-sm font-bold text-gray-900">
                    {metric.name.includes('Rate') ? `${metric.current}%` : 
                     metric.name.includes('Efficiency') ? `${metric.current}%` :
                     `${metric.current.toLocaleString()}${metric.name.includes('Production') ? 
                       (metric.name.includes('Milk') ? ' L/day' : ' eggs/day') : ''}`}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div 
                    className="bg-green-600 h-2.5 rounded-full transition-all duration-1000" 
                    style={{ width: `${metric.percentage}%` }}
                  />
                </div>
                <div className="flex justify-between text-xs text-gray-600 mt-1">
                  <span>Target: {metric.target.toLocaleString()}</span>
                  <span>{metric.percentage}% of target</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Analytics;

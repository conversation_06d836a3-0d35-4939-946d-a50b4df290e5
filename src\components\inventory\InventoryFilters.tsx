
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Filter, Download } from 'lucide-react';
import { SearchInput } from '@/components/common/SearchInput';

interface InventoryFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  selectedType: string;
  onTypeChange: (value: string) => void;
}

export const InventoryFilters: React.FC<InventoryFiltersProps> = ({
  searchTerm,
  onSearchChange,
  selectedType,
  onTypeChange
}) => {
  return (
    <Card className="bg-white border border-gray-200">
      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <SearchInput
            placeholder="Search by ID, breed, or owner..."
            value={searchTerm}
            onChange={onSearchChange}
            className="flex-1"
          />
          
          <select 
            value={selectedType}
            onChange={(e) => onTypeChange(e.target.value)}
            className="px-4 py-2 border border-green-300 rounded-lg bg-white focus:border-green-500"
          >
            <option value="all">All Types</option>
            <option value="cattle">Cattle</option>
            <option value="carabao">Carabao</option>
            <option value="goat">Goat</option>
            <option value="chicken">Chicken</option>
          </select>

          <Button variant="outline" className="gap-2 bg-white hover:bg-green-50 border-green-300 text-green-700">
            <Filter className="w-4 h-4" />
            Filters
          </Button>

          <Button variant="outline" className="gap-2 bg-white hover:bg-green-50 border-green-300 text-green-700">
            <Download className="w-4 h-4" />
            Export
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

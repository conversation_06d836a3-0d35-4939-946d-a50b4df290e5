
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { LandingNavigation } from '@/components/landing/LandingNavigation';
import { LandingHero } from '@/components/landing/LandingHero';
import { LandingFeatures } from '@/components/landing/LandingFeatures';
import { LandingBenefits } from '@/components/landing/LandingBenefits';
import { LandingCTA } from '@/components/landing/LandingCTA';
import { LoginModal } from '@/components/landing/LoginModal';

export const Landing = () => {
  const [showLoginModal, setShowLoginModal] = useState(false);
  const { devBypass, user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  // Redirect authenticated users to dashboard
  useEffect(() => {
    if (user) {
      navigate('/', { replace: true });
    }
  }, [user, navigate]);

  const handleDevBypass = () => {
    devBypass();
    toast({
      title: "Developer Access",
      description: "Logged in with developer credentials.",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50">
      <LandingNavigation 
        onSignIn={() => setShowLoginModal(true)}
        onGetStarted={() => setShowLoginModal(true)}
      />
      
      <LandingHero 
        onGetStarted={() => setShowLoginModal(true)}
        onDevBypass={handleDevBypass}
      />
      
      <LandingFeatures />
      
      <LandingBenefits />
      
      <LandingCTA onGetStarted={() => setShowLoginModal(true)} />

      <LoginModal 
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
      />
    </div>
  );
};

export default Landing;


import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Camera, User, Image } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface PostComposerProps {
  onPost?: (content: string, image?: string) => void;
  placeholder?: string;
}

export const PostComposer: React.FC<PostComposerProps> = ({ 
  onPost, 
  placeholder = "Share your farming updates, tips, or experiences..." 
}) => {
  const { user } = useAuth();
  const [content, setContent] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);

  const handlePost = () => {
    if (content.trim()) {
      onPost?.(content);
      setContent('');
      setIsExpanded(false);
    }
  };

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-6">
      <div className="flex items-start gap-3">
        <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
          <User className="w-5 h-5 text-gray-600" />
        </div>
        <div className="flex-1">
          <Textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            onFocus={() => setIsExpanded(true)}
            placeholder={placeholder}
            className="min-h-[60px] resize-none border-0 p-0 text-lg placeholder:text-gray-500 focus-visible:ring-0"
          />
          
          {isExpanded && (
            <div className="mt-4">
              <div className="flex items-center gap-2 mb-4">
                <Button variant="ghost" size="sm" className="gap-2 text-gray-600">
                  <Image className="w-4 h-4" />
                  Photo
                </Button>
                <Button variant="ghost" size="sm" className="gap-2 text-gray-600">
                  <Camera className="w-4 h-4" />
                  Camera
                </Button>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-500">
                  Posting as {user?.name}
                </div>
                <div className="flex gap-2">
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => {
                      setIsExpanded(false);
                      setContent('');
                    }}
                  >
                    Cancel
                  </Button>
                  <Button 
                    size="sm"
                    onClick={handlePost}
                    disabled={!content.trim()}
                    className="bg-black text-white hover:bg-gray-800"
                  >
                    Post
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

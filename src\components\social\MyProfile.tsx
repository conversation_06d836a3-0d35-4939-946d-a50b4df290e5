
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { User, MapPin, Calendar, Users, Settings, Camera, Award } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { PostComposer } from './PostComposer';
import { PostCard, Post } from './PostCard';

export const MyProfile: React.FC = () => {
  const { user } = useAuth();
  
  const mockStats = {
    livestock: 450,
    followers: 234,
    following: 156,
    posts: 89,
  };

  const mockAchievements = [
    { title: 'Health Champion', description: '99% health success rate', icon: Award },
    { title: 'Productivity Master', description: '30% above average', icon: Award },
    { title: 'Community Leader', description: '100+ connections', icon: Users },
  ];

  const [posts, setPosts] = useState<Post[]>([
    {
      id: '1',
      author: {
        name: user?.name || 'Your Name',
        farmName: 'Green Valley Ranch',
      },
      content: 'Just finished the morning health check on our cattle. All 450 head are looking healthy and strong! The new feeding schedule we implemented last month is really showing results. Our productivity is up 15% compared to last quarter.',
      timestamp: '2 hours ago',
      likes: 23,
      comments: 5,
      isLiked: false,
    },
    {
      id: '2',
      author: {
        name: user?.name || 'Your Name',
        farmName: 'Green Valley Ranch',
      },
      content: 'Excited to share that we\'ve successfully implemented a new pasture rotation system! This sustainable approach will help maintain soil health while maximizing grazing efficiency. Already seeing improvements in grass quality.',
      timestamp: '1 day ago',
      likes: 45,
      comments: 12,
      isLiked: true,
    },
    {
      id: '3',
      author: {
        name: user?.name || 'Your Name',
        farmName: 'Green Valley Ranch',
      },
      content: 'Weather forecast looks perfect for the next week. Planning to move the herd to the north pasture tomorrow. The grass there has recovered beautifully from the rain we had last month.',
      timestamp: '3 days ago',
      likes: 18,
      comments: 8,
      isLiked: false,
    },
  ]);

  const handleNewPost = (content: string, image?: string) => {
    const newPost: Post = {
      id: Date.now().toString(),
      author: {
        name: user?.name || 'Your Name',
        farmName: 'Green Valley Ranch',
      },
      content,
      image,
      timestamp: 'Just now',
      likes: 0,
      comments: 0,
      isLiked: false,
    };
    setPosts([newPost, ...posts]);
  };

  const handleLike = (postId: string) => {
    setPosts(prev => prev.map(post => 
      post.id === postId 
        ? { 
            ...post, 
            isLiked: !post.isLiked,
            likes: post.isLiked ? post.likes - 1 : post.likes + 1
          }
        : post
    ));
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Profile Header */}
      <div className="bg-gradient-to-r from-green-500 to-blue-600 rounded-3xl p-8 text-white mb-8">
        <div className="flex items-center gap-6 mb-6">
          <div className="relative">
            <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
              <User className="w-12 h-12" />
            </div>
            <Button size="sm" className="absolute -bottom-2 -right-2 w-8 h-8 p-0 bg-white text-gray-900 hover:bg-gray-100">
              <Camera className="w-4 h-4" />
            </Button>
          </div>
          <div className="flex-1">
            <h1 className="text-3xl font-bold mb-2">{user?.name || 'Your Name'}</h1>
            <p className="text-xl opacity-90 mb-2">Green Valley Ranch</p>
            <div className="flex items-center gap-4 text-sm opacity-80">
              <div className="flex items-center gap-1">
                <MapPin className="w-4 h-4" />
                <span>Texas, USA</span>
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                <span>Joined March 2023</span>
              </div>
            </div>
          </div>
          <Button variant="outline" className="text-white border-white hover:bg-white hover:text-green-600">
            <Settings className="w-4 h-4 mr-2" />
            Edit Profile
          </Button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold">{mockStats.livestock}</div>
            <div className="text-sm opacity-80">Livestock</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{mockStats.followers}</div>
            <div className="text-sm opacity-80">Followers</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{mockStats.following}</div>
            <div className="text-sm opacity-80">Following</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{posts.length}</div>
            <div className="text-sm opacity-80">Posts</div>
          </div>
        </div>
      </div>

      {/* Achievements */}
      <div className="bg-white rounded-3xl shadow-lg border border-gray-100 p-8 mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Achievements</h2>
        <div className="grid md:grid-cols-3 gap-6">
          {mockAchievements.map((achievement, index) => (
            <div key={index} className="text-center p-6 rounded-2xl bg-gradient-to-br from-green-50 to-blue-50 border border-green-100">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <achievement.icon className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">{achievement.title}</h3>
              <p className="text-sm text-gray-600">{achievement.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Timeline Section */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-gray-900">My Timeline</h2>
        
        {/* Post Composer */}
        <PostComposer onPost={handleNewPost} />

        {/* Posts Feed */}
        <div>
          {posts.map((post) => (
            <PostCard
              key={post.id}
              post={post}
              onLike={handleLike}
              isOwnPost={true}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

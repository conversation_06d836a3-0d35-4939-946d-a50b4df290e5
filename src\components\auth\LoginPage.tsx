import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Eye, EyeOff, Loader2, Zap, ArrowLeft } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';

export const LoginPage = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [loginForm, setLoginForm] = useState({ email: '', password: '' });
  const [signupForm, setSignupForm] = useState({ email: '', password: '', name: '', confirmPassword: '' });
  const { login, signup, isLoading, devBypass } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    const success = await login(loginForm.email, loginForm.password);
    
    if (success) {
      toast({
        title: "Welcome back!",
        description: "You have successfully signed in.",
      });
      navigate('/');
    } else {
      toast({
        title: "Login Failed",
        description: "Invalid email or password. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (signupForm.password !== signupForm.confirmPassword) {
      toast({
        title: "Password Mismatch",
        description: "Passwords do not match. Please try again.",
        variant: "destructive",
      });
      return;
    }
    
    const success = await signup(signupForm.email, signupForm.password, signupForm.name);
    
    if (success) {
      toast({
        title: "Account Created!",
        description: "Welcome to AgriPulse! Your account has been created successfully.",
      });
      navigate('/');
    } else {
      toast({
        title: "Signup Failed",
        description: "Please check your information and try again.",
        variant: "destructive",
      });
    }
  };

  const handleDevBypass = () => {
    console.log('Developer bypass clicked');
    devBypass();
    toast({
      title: "Developer Access",
      description: "Logged in with developer credentials.",
    });
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-md mx-auto pt-16 px-6">
        {/* Back to home link */}
        <Button
          variant="ghost"
          onClick={() => navigate('/landing')}
          className="mb-12 text-gray-600 hover:text-black p-0 h-auto font-normal"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to home
        </Button>

        {/* Sign in form */}
        <div className="space-y-8">
          <div>
            <h1 className="text-3xl font-semibold text-black mb-2">Sign in</h1>
            <p className="text-gray-600">Enter your credentials to access your account</p>
          </div>

          <Tabs defaultValue="login" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-8 bg-gray-100 p-1 h-12 rounded-lg">
              <TabsTrigger 
                value="login" 
                className="data-[state=active]:bg-black data-[state=active]:text-white data-[state=inactive]:bg-gray-200 data-[state=inactive]:text-black rounded-md text-sm font-medium transition-all"
              >
                Sign In
              </TabsTrigger>
              <TabsTrigger 
                value="signup"
                className="data-[state=active]:bg-black data-[state=active]:text-white data-[state=inactive]:bg-gray-200 data-[state=inactive]:text-black rounded-md text-sm font-medium transition-all"
              >
                Sign Up
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="login" className="space-y-6">
              <form onSubmit={handleLogin} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="login-email" className="text-sm font-medium text-black">Email</Label>
                  <Input
                    id="login-email"
                    type="email"
                    placeholder="Enter your email"
                    value={loginForm.email}
                    onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}
                    required
                    className="h-12 bg-white border-gray-300 rounded-lg focus:border-black focus:ring-0"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="login-password" className="text-sm font-medium text-black">Password</Label>
                  <div className="relative">
                    <Input
                      id="login-password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter your password"
                      value={loginForm.password}
                      onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
                      required
                      className="h-12 bg-white border-gray-300 rounded-lg focus:border-black focus:ring-0 pr-12"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                  </div>
                </div>
                
                <Button 
                  type="submit" 
                  className="w-full h-12 bg-black hover:bg-gray-800 text-white rounded-lg font-medium" 
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Signing In...
                    </>
                  ) : (
                    'Sign In'
                  )}
                </Button>
              </form>
            </TabsContent>
            
            <TabsContent value="signup" className="space-y-6">
              <form onSubmit={handleSignup} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="signup-name" className="text-sm font-medium text-black">Full Name</Label>
                  <Input
                    id="signup-name"
                    type="text"
                    placeholder="Enter your full name"
                    value={signupForm.name}
                    onChange={(e) => setSignupForm({ ...signupForm, name: e.target.value })}
                    required
                    className="h-12 bg-white border-gray-300 rounded-lg focus:border-black focus:ring-0"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="signup-email" className="text-sm font-medium text-black">Email</Label>
                  <Input
                    id="signup-email"
                    type="email"
                    placeholder="Enter your email"
                    value={signupForm.email}
                    onChange={(e) => setSignupForm({ ...signupForm, email: e.target.value })}
                    required
                    className="h-12 bg-white border-gray-300 rounded-lg focus:border-black focus:ring-0"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="signup-password" className="text-sm font-medium text-black">Password</Label>
                  <Input
                    id="signup-password"
                    type="password"
                    placeholder="Create a password"
                    value={signupForm.password}
                    onChange={(e) => setSignupForm({ ...signupForm, password: e.target.value })}
                    required
                    className="h-12 bg-white border-gray-300 rounded-lg focus:border-black focus:ring-0"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="signup-confirm" className="text-sm font-medium text-black">Confirm Password</Label>
                  <Input
                    id="signup-confirm"
                    type="password"
                    placeholder="Confirm your password"
                    value={signupForm.confirmPassword}
                    onChange={(e) => setSignupForm({ ...signupForm, confirmPassword: e.target.value })}
                    required
                    className="h-12 bg-white border-gray-300 rounded-lg focus:border-black focus:ring-0"
                  />
                </div>
                
                <Button 
                  type="submit" 
                  className="w-full h-12 bg-black hover:bg-gray-800 text-white rounded-lg font-medium" 
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Creating Account...
                    </>
                  ) : (
                    'Create Account'
                  )}
                </Button>
              </form>
            </TabsContent>
          </Tabs>

          {/* Developer Bypass */}
          <div className="pt-6 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={handleDevBypass}
              className="w-full h-12 text-gray-600 border-gray-300 hover:bg-gray-100 rounded-lg font-medium"
            >
              <Zap className="w-4 h-4 mr-2" />
              Developer Access (Bypass)
            </Button>
            <p className="text-xs text-gray-500 text-center mt-2">
              For development purposes only
            </p>
          </div>

          {/* Footer */}
          <div className="text-center text-sm text-gray-500 pb-8">
            <p>
              By signing in, you agree to our{' '}
              <a href="#" className="text-black hover:underline font-medium">Terms of Service</a>
              {' '}and{' '}
              <a href="#" className="text-black hover:underline font-medium">Privacy Policy</a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};


import React from 'react';
import { BarChart3, Shield, Zap, Users, Brain, Globe, ArrowRight } from 'lucide-react';

const features = [
  {
    icon: BarChart3,
    title: "Advanced Analytics",
    description: "Real-time insights into your livestock performance with detailed reports and trend analysis.",
    gradient: "from-blue-500 to-blue-600"
  },
  {
    icon: Shield,
    title: "Health Monitoring",
    description: "AI-powered disease prediction and health tracking to keep your livestock in optimal condition.",
    gradient: "from-green-500 to-green-600"
  },
  {
    icon: Zap,
    title: "Smart Automation",
    description: "Automated feeding schedules, medication reminders, and breeding cycle management.",
    gradient: "from-yellow-500 to-orange-500"
  },
  {
    icon: Users,
    title: "Team Collaboration",
    description: "Multi-user access with role-based permissions for your farm management team.",
    gradient: "from-purple-500 to-purple-600"
  },
  {
    icon: Brain,
    title: "AI Predictions",
    description: "Machine learning algorithms predict optimal breeding times and health issues before they occur.",
    gradient: "from-indigo-500 to-indigo-600"
  },
  {
    icon: Globe,
    title: "Global Compliance",
    description: "Automated compliance reporting for international livestock regulations and standards.",
    gradient: "from-teal-500 to-teal-600"
  }
];

export const LandingFeatures: React.FC = () => {
  return (
    <section className="py-24 px-4 sm:px-6 lg:px-8 bg-white relative">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-50">
        <div className="absolute inset-0 bg-gray-50"></div>
      </div>
      
      <div className="relative max-w-7xl mx-auto">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Everything you need to manage your livestock
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive tools designed for modern agricultural operations with cutting-edge technology
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-3xl blur-xl"></div>
              <div className="relative bg-white p-8 rounded-3xl border border-gray-100 hover:border-gray-200 transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 group">
                <div className={`w-16 h-16 bg-gradient-to-r ${feature.gradient} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">{feature.title}</h3>
                <p className="text-gray-600 leading-relaxed">{feature.description}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Call to action */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-green-50 to-blue-50 border border-green-200">
            <span className="text-gray-700 font-medium">Ready to get started?</span>
            <ArrowRight className="ml-2 w-4 h-4 text-green-600" />
          </div>
        </div>
      </div>
    </section>
  );
};

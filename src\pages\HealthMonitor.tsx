import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Activity, Thermometer, Wind } from 'lucide-react';
import { HealthAlerts } from '@/components/health/HealthAlerts';
import { HealthStats } from '@/components/health/HealthStats';

const HealthMonitor = () => {
  const [symptoms, setSymptoms] = useState('');
  const [temperature, setTemperature] = useState('');
  const [environment, setEnvironment] = useState('');
  const [riskLevel, setRiskLevel] = useState<number | null>(null);

  const handlePrediction = () => {
    const risk = Math.random() * 100;
    setRiskLevel(risk);
  };

  const getRiskColor = (risk: number) => {
    if (risk < 30) return 'text-gray-600 bg-gray-100';
    if (risk < 70) return 'text-gray-700 bg-gray-200';
    return 'text-red-600 bg-red-100';
  };

  const getRiskLabel = (risk: number) => {
    if (risk < 30) return 'Low Risk';
    if (risk < 70) return 'Medium Risk';
    return 'High Risk';
  };

  const healthAlerts = [
    { id: 1, message: 'Cattle L001 showing symptoms of fever', severity: 'high' as const, time: '2 hours ago' },
    { id: 2, message: 'Weather alert: Heavy rain expected', severity: 'medium' as const, time: '4 hours ago' },
    { id: 3, message: 'Vaccination reminder for Goat batch', severity: 'low' as const, time: '1 day ago' },
  ];

  const healthStats = {
    healthy: 1456,
    treatment: 23,
    critical: 3,
    scheduled: 8
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Health Monitor</h1>
        <p className="text-gray-600 mt-1">AI-powered disease risk prediction and health tracking</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Disease Risk Predictor */}
        <div className="lg:col-span-2">
          <Card className="bg-white border border-gray-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="w-5 h-5 text-gray-600" />
                Disease Risk Predictor
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <Thermometer className="w-4 h-4 inline mr-1" />
                    Temperature (°C)
                  </label>
                  <Input
                    type="number"
                    placeholder="e.g., 39.5"
                    value={temperature}
                    onChange={(e) => setTemperature(e.target.value)}
                    className="bg-white text-black border-gray-300 focus:border-gray-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <Wind className="w-4 h-4 inline mr-1" />
                    Environment
                  </label>
                  <select 
                    value={environment}
                    onChange={(e) => setEnvironment(e.target.value)}
                    className="w-full px-3 py-2 bg-white text-black border border-gray-300 rounded-lg focus:border-gray-500"
                  >
                    <option value="">Select environment</option>
                    <option value="indoor">Indoor/Shelter</option>
                    <option value="outdoor">Outdoor/Pasture</option>
                    <option value="mixed">Mixed Environment</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Observed Symptoms
                </label>
                <Textarea
                  placeholder="Describe any symptoms: lethargy, loss of appetite, coughing, etc."
                  value={symptoms}
                  onChange={(e) => setSymptoms(e.target.value)}
                  className="bg-white text-black border-gray-300 focus:border-gray-500 resize-none"
                />
              </div>

              <Button 
                onClick={handlePrediction}
                className="w-full"
                disabled={!symptoms || !temperature}
              >
                Analyze Disease Risk
              </Button>

              {riskLevel !== null && (
                <div className="mt-6 p-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl border border-gray-200">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">Risk Assessment</h3>
                    <span className={`px-3 py-1 rounded-full text-sm font-semibold ${getRiskColor(riskLevel)}`}>
                      {getRiskLabel(riskLevel)}
                    </span>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                    <div 
                      className={`h-3 rounded-full transition-all duration-1000 ${
                        riskLevel < 30 ? 'bg-gray-500' : 
                        riskLevel < 70 ? 'bg-gray-600' : 'bg-red-500'
                      }`}
                      style={{ width: `${riskLevel}%` }}
                    />
                  </div>
                  
                  <div className="space-y-2 text-sm text-gray-700">
                    <p><strong>Risk Score:</strong> {riskLevel.toFixed(1)}%</p>
                    <p><strong>Recommendation:</strong> {
                      riskLevel < 30 ? 'Continue regular monitoring' :
                      riskLevel < 70 ? 'Schedule veterinary consultation' :
                      'Immediate veterinary attention required'
                    }</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <HealthAlerts alerts={healthAlerts} />
          <HealthStats stats={healthStats} />
        </div>
      </div>
    </div>
  );
};

export default HealthMonitor;

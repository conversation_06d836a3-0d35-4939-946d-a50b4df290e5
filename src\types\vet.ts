
export interface VetVisit {
  id: number;
  animalId: string;
  animalType: string;
  animalName: string;
  veterinarian: string;
  date: string;
  reason: string;
  diagnosis: string;
  treatment: string;
  status: string;
  cost: number;
  nextVisit: string;
}

export interface Medication {
  id: number;
  animalId: string;
  animalName: string;
  medication: string;
  dosage: string;
  frequency: string;
  startDate: string;
  endDate: string;
  status: string;
  veterinarian: string;
}

export interface Reminder {
  id: number;
  type: string;
  animalId: string;
  animalName: string;
  description: string;
  dueDate: string;
  priority: string;
}

export type TabType = 'visits' | 'medications' | 'reminders';

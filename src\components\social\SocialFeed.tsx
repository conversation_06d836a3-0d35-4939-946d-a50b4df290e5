
import React, { useState } from 'react';
import { ProfileCard } from './ProfileCard';
import { PostCard, Post } from './PostCard';
import { Button } from '@/components/ui/button';
import { Search, Users, Globe, BookOpen } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

const mockProfiles = [
  {
    id: '1',
    name: '<PERSON>',
    farmName: 'Green Valley Ranch',
    location: 'Texas, USA',
    joinedDate: 'March 2023',
    friendsCount: 124,
    isFollowing: false,
  },
  {
    id: '2',
    name: '<PERSON>',
    farmName: 'Sunrise Farms',
    location: 'California, USA',
    joinedDate: 'January 2023',
    friendsCount: 89,
    isFollowing: true,
  },
  {
    id: '3',
    name: '<PERSON>',
    farmName: 'Golden Acres',
    location: 'Colorado, USA',
    joinedDate: 'August 2022',
    friendsCount: 156,
    isFollowing: false,
  },
  {
    id: '4',
    name: '<PERSON>',
    farmName: 'Mountain View Farm',
    location: 'Montana, USA',
    joinedDate: 'June 2023',
    friendsCount: 67,
    isFollowing: false,
  },
];

const mockPosts: Post[] = [
  {
    id: '1',
    author: {
      name: '<PERSON>',
      farmName: 'Green Valley Ranch',
    },
    content: 'Just implemented a new automated feeding system! The cattle are adapting well and we\'re already seeing a 20% improvement in feed efficiency. Technology really is transforming modern farming.',
    timestamp: '3 hours ago',
    likes: 42,
    comments: 12,
    isLiked: false,
  },
  {
    id: '2',
    author: {
      name: '<PERSON> Chen',
      farmName: 'Sunrise Farms',
    },
    content: 'Beautiful morning checking on the herd! The new pasture rotation system is working wonders. Grass quality has improved significantly and the cattle are healthier than ever.',
    timestamp: '6 hours ago',
    likes: 78,
    comments: 23,
    isLiked: true,
  },
  {
    id: '3',
    author: {
      name: 'Emma Rodriguez',
      farmName: 'Golden Acres',
    },
    content: 'Sharing some insights from our sustainable farming workshop. Key takeaway: soil health is everything! Implemented cover cropping and already seeing amazing results in just 3 months.',
    timestamp: '1 day ago',
    likes: 95,
    comments: 34,
    isLiked: false,
  },
  {
    id: '4',
    author: {
      name: 'David Thompson',
      farmName: 'Mountain View Farm',
    },
    content: 'Weather challenges this week, but our herd management system helped us move the cattle to shelter just in time. Grateful for modern technology and proper planning!',
    timestamp: '2 days ago',
    likes: 56,
    comments: 18,
    isLiked: true,
  },
];

export const SocialFeed: React.FC = () => {
  const { user } = useAuth();
  const [profiles, setProfiles] = useState(mockProfiles);
  const [posts, setPosts] = useState(mockPosts);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<'feed' | 'farmers'>('feed');

  const handleToggleFollow = (profileId: string) => {
    setProfiles(prev => prev.map(profile => 
      profile.id === profileId 
        ? { ...profile, isFollowing: !profile.isFollowing }
        : profile
    ));
  };

  const handleLike = (postId: string) => {
    setPosts(prev => prev.map(post => 
      post.id === postId 
        ? { 
            ...post, 
            isLiked: !post.isLiked,
            likes: post.isLiked ? post.likes - 1 : post.likes + 1
          }
        : post
    ));
  };

  const filteredProfiles = profiles.filter(profile =>
    profile.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    profile.farmName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    profile.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const followingPosts = posts.filter(post => 
    profiles.find(profile => 
      profile.name === post.author.name && profile.isFollowing
    )
  );

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Users className="w-8 h-8 text-green-600" />
          <h2 className="text-3xl font-bold text-gray-900">Community</h2>
        </div>
        <p className="text-gray-600 mb-6">Connect with fellow farmers and share your experiences</p>
        
        <div className="relative mb-6">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder={activeTab === 'feed' ? "Search posts..." : "Search farmers by name, farm, or location..."}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        </div>

        <div className="flex items-center gap-4">
          <Button 
            variant={activeTab === 'feed' ? 'default' : 'outline'} 
            size="sm"
            onClick={() => setActiveTab('feed')}
            className={activeTab === 'feed' ? 'bg-black text-white hover:bg-gray-800' : ''}
          >
            <BookOpen className="w-4 h-4 mr-2" />
            News Feed
          </Button>
          <Button 
            variant={activeTab === 'farmers' ? 'default' : 'outline'} 
            size="sm"
            onClick={() => setActiveTab('farmers')}
            className={activeTab === 'farmers' ? 'bg-black text-white hover:bg-gray-800' : ''}
          >
            <Users className="w-4 h-4 mr-2" />
            Find Farmers
          </Button>
          {activeTab === 'farmers' && (
            <Button variant="outline" size="sm">
              <Globe className="w-4 h-4 mr-2" />
              Following ({profiles.filter(p => p.isFollowing).length})
            </Button>
          )}
        </div>
      </div>

      {activeTab === 'feed' ? (
        <div className="space-y-6">
          {followingPosts.length > 0 ? (
            followingPosts.map((post) => (
              <PostCard
                key={post.id}
                post={post}
                onLike={handleLike}
              />
            ))
          ) : (
            <div className="text-center py-12 bg-white rounded-2xl border border-gray-100">
              <BookOpen className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-500 mb-2">No posts in your feed</h3>
              <p className="text-gray-400 mb-4">Follow some farmers to see their posts here</p>
              <Button 
                onClick={() => setActiveTab('farmers')}
                className="bg-black text-white hover:bg-gray-800"
              >
                Find Farmers to Follow
              </Button>
            </div>
          )}
        </div>
      ) : (
        <div className="grid md:grid-cols-2 gap-6">
          {filteredProfiles.map((profile) => (
            <ProfileCard
              key={profile.id}
              profile={profile}
              onToggleFollow={handleToggleFollow}
            />
          ))}
        </div>
      )}

      {activeTab === 'farmers' && filteredProfiles.length === 0 && (
        <div className="text-center py-12">
          <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-500 mb-2">No farmers found</h3>
          <p className="text-gray-400">Try adjusting your search terms</p>
        </div>
      )}
    </div>
  );
};

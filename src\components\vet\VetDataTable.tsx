
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FileText, Clock } from 'lucide-react';
import { VetVisit, Medication, TabType } from '@/types/vet';
import { getStatusColor } from '@/utils/vetUtils';

interface VetDataTableProps {
  data: (VetVisit | Medication)[];
  selectedTab: TabType;
}

export const VetDataTable: React.FC<VetDataTableProps> = ({ data, selectedTab }) => {
  return (
    <Card className="bg-white border border-gray-200">
      <CardHeader>
        <CardTitle className="text-gray-900">
          {selectedTab === 'visits' ? 'Veterinary Visits' : 'Medication Records'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Animal</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">
                  {selectedTab === 'visits' ? 'Veterinarian' : 'Medication'}
                </th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Date</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">
                  {selectedTab === 'visits' ? 'Reason' : 'Dosage'}
                </th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Status</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Actions</th>
              </tr>
            </thead>
            <tbody>
              {data.map((record) => (
                <tr key={record.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 px-4">
                    <div>
                      <p className="font-semibold text-gray-900">{record.animalName}</p>
                      <p className="text-sm text-gray-600">
                        {record.animalId} • {selectedTab === 'visits' ? (record as VetVisit).animalType : 'Animal'}
                      </p>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-gray-700">
                    {selectedTab === 'visits' ? (record as VetVisit).veterinarian : (record as Medication).medication}
                  </td>
                  <td className="py-3 px-4 text-gray-700">
                    {selectedTab === 'visits' 
                      ? new Date((record as VetVisit).date).toLocaleDateString()
                      : new Date((record as Medication).startDate).toLocaleDateString()
                    }
                  </td>
                  <td className="py-3 px-4 text-gray-700">
                    {selectedTab === 'visits' ? (record as VetVisit).reason : `${(record as Medication).dosage} ${(record as Medication).frequency}`}
                  </td>
                  <td className="py-3 px-4">
                    <Badge className={getStatusColor(record.status)}>
                      {record.status}
                    </Badge>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" className="border-gray-300 hover:bg-gray-50">
                        <FileText className="w-4 h-4" />
                      </Button>
                      {selectedTab === 'visits' && record.status === 'follow-up' && (
                        <Button size="sm" className="bg-gray-800 hover:bg-gray-900 text-white">
                          <Clock className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};

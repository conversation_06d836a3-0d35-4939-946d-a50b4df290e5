
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { InventoryFilters } from '@/components/inventory/InventoryFilters';
import { InventoryTable } from '@/components/inventory/InventoryTable';

const Inventory = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');

  const livestockInventory = [
    { id: 'L001', type: 'Cattle', breed: 'Holstein', age: '2 years', status: 'Healthy', owner: '<PERSON>', lastCheckup: '2024-05-15' },
    { id: 'L002', type: 'Carabao', breed: 'Philippine Carabao', age: '3 years', status: 'Treatment', owner: '<PERSON>', lastCheckup: '2024-05-20' },
    { id: 'L003', type: 'Goat', breed: 'Boer', age: '1 year', status: 'Healthy', owner: '<PERSON>', lastCheckup: '2024-05-18' },
    { id: 'L004', type: 'Chicken', breed: 'Rhode Island Red', age: '6 months', status: 'Healthy', owner: '<PERSON>', lastCheckup: '2024-05-22' },
    { id: 'L005', type: 'Cattle', breed: 'Brahman', age: '4 years', status: 'Critical', owner: 'Jose Martinez', lastCheckup: '2024-05-10' },
  ];

  const filteredInventory = livestockInventory.filter(item => 
    (selectedType === 'all' || item.type.toLowerCase() === selectedType) &&
    (searchTerm === '' || 
     item.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
     item.breed.toLowerCase().includes(searchTerm.toLowerCase()) ||
     item.owner.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Livestock Inventory</h1>
          <p className="text-gray-600 mt-1">Manage and track all your livestock records</p>
        </div>
        <Button className="bg-green-600 hover:bg-green-700 text-white gap-2">
          <Plus className="w-4 h-4" />
          Add Livestock
        </Button>
      </div>

      {/* Filters */}
      <InventoryFilters
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        selectedType={selectedType}
        onTypeChange={setSelectedType}
      />

      {/* Inventory Table */}
      <InventoryTable data={filteredInventory} />
    </div>
  );
};

export default Inventory;


# AgriPulse - Livestock Management System

## Project Overview

AgriPulse is a comprehensive livestock inventory and analytics tool designed for modern agricultural operations. Monitor livestock health, track performance metrics, and optimize farm productivity with AI-powered insights.

## Features

- **Livestock Inventory Management**: Track and manage your livestock with detailed records
- **Health Monitoring**: Monitor animal health status and receive alerts
- **Analytics Dashboard**: Get insights into productivity metrics and trends
- **Social Community**: Connect with other farmers and share experiences
- **Veterinary Tracking**: Keep track of veterinary appointments and treatments
- **Price Forecasting**: Get market insights and price predictions

## Technology Stack

This project is built with modern web technologies:

- **React** - Frontend framework
- **TypeScript** - Type safety and better development experience
- **Vite** - Fast build tool and development server
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - Modern UI components
- **Recharts** - Data visualization
- **React Router** - Client-side routing

## Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm or yarn package manager

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd agripulse
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:8080`

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Project Structure

```
src/
├── components/          # React components
│   ├── auth/           # Authentication components
│   ├── common/         # Reusable components
│   ├── health/         # Health monitoring components
│   ├── inventory/      # Inventory management components
│   ├── landing/        # Landing page components
│   ├── social/         # Social features components
│   ├── ui/             # UI components (shadcn/ui)
│   └── vet/            # Veterinary tracking components
├── contexts/           # React contexts
├── hooks/              # Custom React hooks
├── lib/                # Utility libraries
├── pages/              # Page components
├── types/              # TypeScript type definitions
└── utils/              # Utility functions
```

## Deployment

The application can be deployed to any static hosting service such as:

- Vercel
- Netlify
- AWS S3 + CloudFront
- GitHub Pages

Build the project using `npm run build` and deploy the `dist` folder.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please contact the AgriPulse team or create an issue in the repository.

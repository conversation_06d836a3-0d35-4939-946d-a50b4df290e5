
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Heart, MessageCircle, Share, MoreHorizontal, User } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface Post {
  id: string;
  author: {
    name: string;
    farmName: string;
    avatar?: string;
  };
  content: string;
  image?: string;
  timestamp: string;
  likes: number;
  comments: number;
  isLiked?: boolean;
}

interface PostCardProps {
  post: Post;
  onLike?: (postId: string) => void;
  onComment?: (postId: string) => void;
  onShare?: (postId: string) => void;
  isOwnPost?: boolean;
}

export const PostCard: React.FC<PostCardProps> = ({ 
  post, 
  onLike, 
  onComment, 
  onShare,
  isOwnPost = false 
}) => {
  const [isLiked, setIsLiked] = useState(post.isLiked || false);
  const [likes, setLikes] = useState(post.likes);

  const handleLike = () => {
    setIsLiked(!isLiked);
    setLikes(prev => isLiked ? prev - 1 : prev + 1);
    onLike?.(post.id);
  };

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-6">
      {/* Post Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
            <User className="w-5 h-5 text-gray-600" />
          </div>
          <div>
            <h4 className="font-semibold text-gray-900">{post.author.name}</h4>
            <div className="flex items-center gap-2 text-sm text-gray-500">
              <span>{post.author.farmName}</span>
              <span>•</span>
              <span>{post.timestamp}</span>
            </div>
          </div>
        </div>
        {isOwnPost && (
          <Button variant="ghost" size="sm" className="text-gray-400 hover:text-gray-600">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        )}
      </div>

      {/* Post Content */}
      <div className="mb-4">
        <p className="text-gray-800 leading-relaxed">{post.content}</p>
      </div>

      {/* Post Image */}
      {post.image && (
        <div className="mb-4 rounded-xl overflow-hidden">
          <img 
            src={post.image} 
            alt="Post content" 
            className="w-full h-64 object-cover"
          />
        </div>
      )}

      {/* Engagement Stats */}
      <div className="flex items-center justify-between py-3 border-t border-gray-100 mb-3">
        <div className="flex items-center gap-4 text-sm text-gray-500">
          <span>{likes} likes</span>
          <span>{post.comments} comments</span>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-2 border-t border-gray-100">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleLike}
          className={cn(
            "flex-1 gap-2",
            isLiked ? "text-red-600 hover:text-red-700" : "text-gray-600 hover:text-gray-700"
          )}
        >
          <Heart className={cn("w-4 h-4", isLiked && "fill-current")} />
          Like
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onComment?.(post.id)}
          className="flex-1 gap-2 text-gray-600 hover:text-gray-700"
        >
          <MessageCircle className="w-4 h-4" />
          Comment
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onShare?.(post.id)}
          className="flex-1 gap-2 text-gray-600 hover:text-gray-700"
        >
          <Share className="w-4 h-4" />
          Share
        </Button>
      </div>
    </div>
  );
};

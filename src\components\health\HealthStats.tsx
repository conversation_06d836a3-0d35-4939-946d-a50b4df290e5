
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';

interface HealthStatsProps {
  stats: {
    healthy: number;
    treatment: number;
    critical: number;
    scheduled: number;
  };
}

export const HealthStats: React.FC<HealthStatsProps> = ({ stats }) => {
  return (
    <Card className="bg-white border border-gray-200">
      <CardHeader>
        <CardTitle className="text-lg">Today's Health Stats</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex justify-between items-center">
          <span className="text-gray-600">Healthy Animals</span>
          <span className="font-bold text-gray-600">{stats.healthy.toLocaleString()}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-gray-600">Under Treatment</span>
          <span className="font-bold text-gray-700">{stats.treatment}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-gray-600">Critical Cases</span>
          <span className="font-bold text-red-600">{stats.critical}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-gray-600">Vet Visits Scheduled</span>
          <span className="font-bold text-gray-600">{stats.scheduled}</span>
        </div>
      </CardContent>
    </Card>
  );
};


import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from 'recharts';
import { TrendingUp, TrendingDown, DollarSign, Calendar, Target } from 'lucide-react';

const PriceForecast = () => {
  const [selectedAnimal, setSelectedAnimal] = useState('cattle');
  const [timeRange, setTimeRange] = useState('30');

  // Mock price data with forecasting
  const priceData = {
    cattle: [
      { date: '2024-05-01', actual: 45000, predicted: null, confidence: null },
      { date: '2024-05-08', actual: 47000, predicted: null, confidence: null },
      { date: '2024-05-15', actual: 46500, predicted: null, confidence: null },
      { date: '2024-05-22', actual: 48000, predicted: null, confidence: null },
      { date: '2024-05-29', actual: 49500, predicted: null, confidence: null },
      { date: '2024-06-05', actual: null, predicted: 51000, confidence: { low: 48500, high: 53500 } },
      { date: '2024-06-12', actual: null, predicted: 52500, confidence: { low: 49800, high: 55200 } },
      { date: '2024-06-19', actual: null, predicted: 54000, confidence: { low: 51000, high: 57000 } },
      { date: '2024-06-26', actual: null, predicted: 55500, confidence: { low: 52200, high: 58800 } },
    ],
    goat: [
      { date: '2024-05-01', actual: 8500, predicted: null, confidence: null },
      { date: '2024-05-08', actual: 8800, predicted: null, confidence: null },
      { date: '2024-05-15', actual: 8600, predicted: null, confidence: null },
      { date: '2024-05-22', actual: 9200, predicted: null, confidence: null },
      { date: '2024-05-29', actual: 9500, predicted: null, confidence: null },
      { date: '2024-06-05', actual: null, predicted: 9800, confidence: { low: 9300, high: 10300 } },
      { date: '2024-06-12', actual: null, predicted: 10200, confidence: { low: 9600, high: 10800 } },
      { date: '2024-06-19', actual: null, predicted: 10500, confidence: { low: 9900, high: 11100 } },
      { date: '2024-06-26', actual: null, predicted: 10800, confidence: { low: 10100, high: 11500 } },
    ],
    chicken: [
      { date: '2024-05-01', actual: 180, predicted: null, confidence: null },
      { date: '2024-05-08', actual: 185, predicted: null, confidence: null },
      { date: '2024-05-15', actual: 190, predicted: null, confidence: null },
      { date: '2024-05-22', actual: 195, predicted: null, confidence: null },
      { date: '2024-05-29', actual: 200, predicted: null, confidence: null },
      { date: '2024-06-05', actual: null, predicted: 205, confidence: { low: 195, high: 215 } },
      { date: '2024-06-12', actual: null, predicted: 210, confidence: { low: 200, high: 220 } },
      { date: '2024-06-19', actual: null, predicted: 215, confidence: { low: 205, high: 225 } },
      { date: '2024-06-26', actual: null, predicted: 220, confidence: { low: 210, high: 230 } },
    ],
  };

  const marketInsights = {
    cattle: {
      trend: 'up',
      change: '+12.3%',
      recommendation: 'Hold for 2-3 weeks for optimal price',
      factors: ['High demand for quality beef', 'Limited supply due to drought', 'Festival season approaching']
    },
    goat: {
      trend: 'up',
      change: '+8.7%',
      recommendation: 'Good time to sell',
      factors: ['Increased demand for goat meat', 'Export opportunities growing', 'Competitive prices']
    },
    chicken: {
      trend: 'up',
      change: '+5.2%',
      recommendation: 'Stable market, sell as needed',
      factors: ['Steady local demand', 'Feed costs stabilizing', 'Regular market conditions']
    }
  };

  const currentData = priceData[selectedAnimal];
  const currentInsight = marketInsights[selectedAnimal];
  const latestPrice = currentData.find(d => d.actual)?.actual || 0;
  const nextPrice = currentData.find(d => d.predicted)?.predicted || 0;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Price Forecast</h1>
        <p className="text-gray-700 mt-1">AI-powered market price predictions and selling recommendations</p>
      </div>

      {/* Control Panel */}
      <div className="flex flex-wrap gap-4">
        <div className="flex gap-2">
          {['cattle', 'goat', 'chicken'].map((animal) => (
            <Button
              key={animal}
              variant={selectedAnimal === animal ? 'default' : 'outline'}
              onClick={() => setSelectedAnimal(animal)}
              className="capitalize"
            >
              {animal}
            </Button>
          ))}
        </div>
        <div className="flex gap-2">
          {[
            { value: '7', label: '7 Days' },
            { value: '30', label: '30 Days' },
            { value: '90', label: '3 Months' }
          ].map((range) => (
            <Button
              key={range.value}
              variant={timeRange === range.value ? 'default' : 'outline'}
              onClick={() => setTimeRange(range.value)}
              size="sm"
            >
              {range.label}
            </Button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Price Chart */}
        <div className="lg:col-span-3">
          <Card className="bg-white border border-gray-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-gray-900">
                <TrendingUp className="w-5 h-5 text-forest-600" />
                Price Forecast - {selectedAnimal.charAt(0).toUpperCase() + selectedAnimal.slice(1)}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={currentData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                    <XAxis 
                      dataKey="date" 
                      stroke="#6b7280"
                      tick={{ fill: '#6b7280' }}
                      tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                    />
                    <YAxis 
                      stroke="#6b7280"
                      tick={{ fill: '#6b7280' }}
                      tickFormatter={(value) => `₱${value.toLocaleString()}`}
                    />
                    <Tooltip 
                      formatter={(value, name) => [`₱${value?.toLocaleString()}`, name === 'actual' ? 'Historical Price' : 'Predicted Price']}
                      labelFormatter={(value) => new Date(value).toLocaleDateString()}
                      contentStyle={{ backgroundColor: 'white', border: '1px solid #d1d5db', borderRadius: '8px' }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="actual" 
                      stroke="#22c55e" 
                      strokeWidth={3}
                      dot={{ fill: '#22c55e', strokeWidth: 2, r: 4 }}
                      connectNulls={false}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="predicted" 
                      stroke="#3b82f6" 
                      strokeWidth={3}
                      strokeDasharray="8 8"
                      dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                      connectNulls={false}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
              
              <div className="mt-4 flex items-center gap-6 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-1 bg-forest-500 rounded"></div>
                  <span>Historical Prices</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-1 bg-blue-500 rounded border-dashed"></div>
                  <span>Predicted Prices</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Market Insights */}
        <div className="space-y-6">
          {/* Current Price */}
          <Card className="bg-white border border-gray-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-gray-900">Current Price</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">₱{latestPrice.toLocaleString()}</div>
              <div className={`flex items-center gap-1 text-sm mt-2 ${
                currentInsight.trend === 'up' ? 'text-forest-600' : 'text-red-600'
              }`}>
                {currentInsight.trend === 'up' ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
                {currentInsight.change} this month
              </div>
            </CardContent>
          </Card>

          {/* Next Week Prediction */}
          <Card className="bg-white border border-gray-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-gray-900">Next Week Forecast</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">₱{nextPrice.toLocaleString()}</div>
              <div className="text-sm text-gray-600 mt-2">
                Expected price range: ₱{currentData.find(d => d.predicted)?.confidence?.low.toLocaleString()} - 
                ₱{currentData.find(d => d.predicted)?.confidence?.high.toLocaleString()}
              </div>
            </CardContent>
          </Card>

          {/* Recommendation */}
          <Card className="bg-gradient-to-br from-amber-50 to-amber-100 border border-amber-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-amber-800 flex items-center gap-2">
                <Target className="w-5 h-5" />
                Recommendation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-amber-900 font-semibold">{currentInsight.recommendation}</p>
              <div className="mt-3 space-y-1">
                <p className="text-sm font-semibold text-amber-800">Market factors:</p>
                {currentInsight.factors.map((factor, index) => (
                  <p key={index} className="text-xs text-amber-700">• {factor}</p>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="bg-white border border-gray-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-gray-900">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full bg-forest-600 hover:bg-forest-700 text-white">
                <DollarSign className="w-4 h-4 mr-2" />
                Record Sale
              </Button>
              <Button variant="outline" className="w-full border-gray-300 text-gray-700">
                <Calendar className="w-4 h-4 mr-2" />
                Set Price Alert
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PriceForecast;

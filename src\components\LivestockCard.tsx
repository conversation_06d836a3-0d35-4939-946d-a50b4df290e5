
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface LivestockCardProps {
  livestock: {
    type: string;
    count: number;
    breed: string;
    icon: string;
  };
  className?: string;
  style?: React.CSSProperties;
}

export const LivestockCard: React.FC<LivestockCardProps> = ({ 
  livestock, 
  className, 
  style 
}) => {
  const getGradientColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'cattle':
        return 'from-forest-500 to-forest-700';
      case 'carabao':
        return 'from-clay-500 to-clay-700';
      case 'goats':
        return 'from-amber-500 to-amber-700';
      case 'chickens':
        return 'from-orange-500 to-red-600';
      default:
        return 'from-forest-500 to-forest-700';
    }
  };

  return (
    <Card 
      className={cn(
        "bg-white/90 backdrop-blur-sm rounded-3xl border-gray-200 hover:border-forest-300 transition-all duration-300 hover:scale-105 hover:shadow-lg group cursor-pointer",
        className
      )}
      style={style}
    >
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className={`text-4xl animate-float group-hover:animate-bounce-gentle`}>
            {livestock.icon}
          </div>
          <div className={`px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r ${getGradientColor(livestock.type)} text-white`}>
            Active
          </div>
        </div>
        
        <div className="space-y-3">
          <h3 className="text-xl font-bold text-forest-700 group-hover:text-forest-600 transition-colors">
            {livestock.type}
          </h3>
          
          <div className="flex items-center justify-between">
            <span className="text-3xl font-bold text-gray-800 group-hover:text-amber-600 transition-colors">
              {livestock.count.toLocaleString()}
            </span>
            <div className="text-right">
              <div className="text-xs text-gray-500 uppercase tracking-wide">Total</div>
            </div>
          </div>
          
          <div className="pt-2 border-t border-gray-200">
            <p className="text-sm text-gray-600 font-dm-sans">
              <span className="text-forest-600 font-medium">Breeds:</span> {livestock.breed}
            </p>
          </div>
          
          {/* Progress bar showing capacity */}
          <div className="space-y-1">
            <div className="flex justify-between text-xs text-gray-500">
              <span>Capacity</span>
              <span>75%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full bg-gradient-to-r ${getGradientColor(livestock.type)} transition-all duration-1000`}
                style={{ width: '75%' }}
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

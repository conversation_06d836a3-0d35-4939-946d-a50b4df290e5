
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  FileText, 
  BarChart3, 
  Activity, 
  TrendingUp, 
  Stethoscope,
  Settings,
  User,
  LogOut,
  Users,
  UserCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';

const navigationItems = [
  { 
    name: 'Dashboard', 
    href: '/', 
    icon: Home,
    description: 'Overview & Quick Stats'
  },
  { 
    name: 'Inventory', 
    href: '/inventory', 
    icon: FileText,
    description: 'Manage Livestock Records'
  },
  { 
    name: 'Analytics', 
    href: '/analytics', 
    icon: BarChart3,
    description: 'Performance Insights'
  },
  { 
    name: 'Health Monitor', 
    href: '/health', 
    icon: Activity,
    description: 'Disease Risk Prediction'
  },
  { 
    name: 'Price Forecast', 
    href: '/forecast', 
    icon: TrendingUp,
    description: 'Market Price Trends'
  },
  { 
    name: 'Vet Tracker', 
    href: '/vet', 
    icon: Stethoscope,
    description: 'Medical Records'
  },
  { 
    name: 'Community', 
    href: '/community', 
    icon: Users,
    description: 'Connect with Farmers'
  },
  { 
    name: 'My Profile', 
    href: '/profile', 
    icon: UserCircle,
    description: 'Your Farmer Profile'
  },
];

interface NavigationProps {
  isOpen: boolean;
  onClose: () => void;
}

export const Navigation: React.FC<NavigationProps> = ({ isOpen, onClose }) => {
  const location = useLocation();
  const { user, logout } = useAuth();

  const handleLogout = () => {
    logout();
    onClose();
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
          onClick={onClose}
        />
      )}
      
      {/* Sidebar */}
      <aside 
        className={cn(
          "fixed left-0 top-0 h-full bg-white border-r border-gray-200 transition-transform duration-300 z-50 w-80",
          isOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
        )}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center gap-3 mb-2">
              <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-700 rounded-2xl flex items-center justify-center">
                <span className="text-white text-xl">🌾</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">AgriPulse</h1>
                <p className="text-sm text-gray-600">Livestock Management</p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
            {navigationItems.map((item) => {
              const isActive = location.pathname === item.href;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={onClose}
                  className={cn(
                    "flex items-center gap-3 p-4 rounded-2xl transition-all duration-200 group",
                    isActive 
                      ? "bg-green-500 text-white shadow-lg" 
                      : "hover:bg-gray-50 text-gray-700 hover:text-green-700"
                  )}
                >
                  <item.icon 
                    className={cn(
                      "w-5 h-5 transition-transform group-hover:scale-110",
                      isActive ? "text-white" : "text-green-600"
                    )} 
                  />
                  <div className="flex-1">
                    <div className="font-semibold">{item.name}</div>
                    <div className={cn(
                      "text-xs opacity-80",
                      isActive ? "text-white" : "text-gray-500"
                    )}>
                      {item.description}
                    </div>
                  </div>
                </Link>
              );
            })}
          </nav>

          {/* User Profile */}
          <div className="p-4 border-t border-gray-200 space-y-2">
            <div className="flex items-center gap-3 p-3 rounded-2xl bg-gray-50">
              <div className="w-10 h-10 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center">
                <User className="w-5 h-5 text-white" />
              </div>
              <div className="flex-1">
                <div className="font-semibold text-gray-900">{user?.name || 'User'}</div>
                <div className="text-sm text-gray-600">{user?.email}</div>
              </div>
              <Settings className="w-4 h-4 text-gray-400" />
            </div>
            
            <Button
              variant="ghost"
              onClick={handleLogout}
              className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Sign Out
            </Button>
          </div>
        </div>
      </aside>
    </>
  );
};


import React from 'react';
import { Button } from '@/components/ui/button';
import { Filter } from 'lucide-react';

interface FilterButtonProps {
  onClick?: () => void;
  children?: React.ReactNode;
}

export const FilterButton: React.FC<FilterButtonProps> = ({ onClick, children }) => {
  return (
    <Button 
      variant="outline" 
      onClick={onClick}
      className="bg-white hover:bg-green-50 border-green-300 text-green-700"
    >
      <Filter className="w-4 h-4 mr-2" />
      {children || "Filter"}
    </Button>
  );
};
